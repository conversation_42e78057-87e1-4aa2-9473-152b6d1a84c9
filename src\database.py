"""Database client for Niche Text ETL."""

import pyodbc
import pymssql
from typing import Dict, Any, List, Iterator
from contextlib import contextmanager
from loguru import logger

from .config import DatabaseConfig


class DbClient:
    """Database client with connection management and batch processing."""

    def __init__(self, config: DatabaseConfig, connection_type: str = "pyodbc"):
        """Initialize database client.

        Args:
            config: Database configuration
            connection_type: Either 'pyodbc' or 'pymssql'
        """
        self.config = config
        self.connection_type = connection_type
        self._connection = None

    def _build_full_table_name(self, table_name: str, schema: str = None) -> str:
        """Build 4-part table name: [server].[database].[schema].[table]

        Args:
            table_name: Simple table name
            schema: Schema name (uses config schema if None)

        Returns:
            Fully qualified table name
        """
        if schema is None:
            schema = self.config.db_schema
        return f"[{self.config.server}].[{self.config.database}].[{schema}].[{table_name}]"
        
    def _build_connection_string(self) -> str:
        """Build connection string based on configuration."""
        if self.connection_type == "pyodbc":
            if self.config.trusted_connection:
                return (
                    f"DRIVER={{{self.config.driver}}};"
                    f"SERVER={self.config.server};"
                    f"DATABASE={self.config.database};"
                    f"Trusted_Connection=yes;"
                    f"Connection Timeout={self.config.connection_timeout};"
                )
            else:
                return (
                    f"DRIVER={{{self.config.driver}}};"
                    f"SERVER={self.config.server};"
                    f"DATABASE={self.config.database};"
                    f"UID={self.config.username};"
                    f"PWD={self.config.password};"
                    f"Connection Timeout={self.config.connection_timeout};"
                )
        else:  # pymssql
            return {
                'server': self.config.server,
                'database': self.config.database,
                'user': self.config.username,
                'password': self.config.password,
                'timeout': self.config.connection_timeout,
                'login_timeout': self.config.connection_timeout
            }
    
    @contextmanager
    def get_connection(self):
        """Get database connection with automatic cleanup."""
        connection = None
        try:
            if self.connection_type == "pyodbc":
                connection_string = self._build_connection_string()
                connection = pyodbc.connect(connection_string)
                connection.timeout = self.config.command_timeout
            else:  # pymssql
                connection_params = self._build_connection_string()
                connection = pymssql.connect(**connection_params)
                
            logger.debug(f"Database connection established to {self.config.server}")
            yield connection
            
        except Exception as e:
            logger.error(f"Database connection error: {e}")
            if connection:
                connection.rollback()
            raise
        finally:
            if connection:
                connection.close()
                logger.debug("Database connection closed")
    
    def test_connection(self) -> bool:
        """Test database connectivity."""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                return result[0] == 1
        except Exception as e:
            logger.error(f"Connection test failed: {e}")
            return False
    
    def get_last_processed_id(self, checkpoint_table: str, process_name: str) -> int:
        """Get the last processed ID from checkpoint table."""
        full_table_name = self._build_full_table_name(checkpoint_table)
        query = f"""
        SELECT ISNULL(MAX(LastProcessedID), 0) as LastProcessedID
        FROM {full_table_name}
        WHERE ProcessName = ?
        """
        
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, (process_name,))
                result = cursor.fetchone()
                last_id = result[0] if result else 0
                logger.info(f"Last processed ID for {process_name}: {last_id}")
                return last_id
        except Exception as e:
            logger.warning(f"Could not retrieve last processed ID for {process_name}: {e}. Starting from 0.")
            return 0
    
    def update_checkpoint(self, checkpoint_table: str, process_name: str, last_processed_id: int) -> None:
        """Update the checkpoint with last processed ID."""
        full_table_name = self._build_full_table_name(checkpoint_table)
        query = f"""
        MERGE {full_table_name} AS target
        USING (SELECT ? as ProcessName, ? as LastProcessedID, GETDATE() as LastUpdated) AS source
        ON target.ProcessName = source.ProcessName
        WHEN MATCHED THEN
            UPDATE SET LastProcessedID = source.LastProcessedID, LastUpdated = source.LastUpdated
        WHEN NOT MATCHED THEN
            INSERT (ProcessName, LastProcessedID, LastUpdated)
            VALUES (source.ProcessName, source.LastProcessedID, source.LastUpdated);
        """
        
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, (process_name, last_processed_id))
                conn.commit()
                logger.debug(f"Checkpoint updated for {process_name}: {last_processed_id}")
        except Exception as e:
            logger.error(f"Failed to update checkpoint for {process_name}: {e}")
            raise

    def fetch_batch(self, batch_size: int, last_processed_id: int,
                   host_id_prefixes: List[str]) -> Iterator[List[Dict[str, Any]]]:
        """Fetch batches of records for processing.

        Args:
            batch_size: Number of records per batch
            last_processed_id: Starting ID for incremental processing
            host_id_prefixes: List of host ID prefixes to filter

        Yields:
            List of record dictionaries
        """
        # Build the main query from your specification
        host_id_filter = "','".join(host_id_prefixes)

        # Build 4-part table names for source tables
        tbl_blob_data = self._build_full_table_name("TBL_BlobData")
        tbl_gocc_tr_event = self._build_full_table_name("TBL_GOccurrenceTREvent")
        tbl_gocc = self._build_full_table_name("TBL_GOccurrence")
        tbl_std_occ_type = self._build_full_table_name("TBL_StandardOccurrenceType")

        query = f"""
        WITH cte AS (
            SELECT TOP (?)
                BD.Id,
                BD.Data,
                BD.Type,
                TR.Id AS Niche_Report_ID,
                TR.GOccReport_EnteredTime AS Entered_Time,
                TR.GOccReport_ReportTime AS Report_Time,
                TR.Remarks,
                TR.GOccReportAuth_RId AS Niche_Author_ID,
                TR.GOccReportEnter_RId AS Niche_Enter_ID,
                OC.Id AS Niche_Occurrence_ID,
                OC.OccurrenceFileNo AS Occurrence_Number,
                SOT.DispatchOccType AS Occurrence_Type,
                OC.ESAreaLevel1 AS Zone,
                OC.ESAreaLevel3 AS Team,
                OC.ESAreaLevel5 AS Municipality,
                OC.AccessControlList,
                CASE WHEN PATINDEX('%[^a-zA-Z0-9]%', BD.Type) = 0 THEN UPPER(BD.Type)
                     WHEN CHARINDEX('.', BD.Type) > 0 THEN UPPER(RIGHT(BD.Type, CHARINDEX('.', REVERSE(BD.Type)) - 1))
                     WHEN BD.Type LIKE '%unknown%' THEN 'UNKNOWN'
                     ELSE UPPER(BD.Type)
                END AS fixed_type,
                IIF(fixed_type LIKE '%;lz%', LEFT(BD.Type, CHARINDEX(';lz', BD.Type) - 1), fixed_type) AS real_type,
                CASE
                  WHEN real_type LIKE 'doc%' THEN 'ms_word'
                  WHEN real_type LIKE 'xls%' THEN 'ms_excel'
                  WHEN real_type IN ('pdf') THEN 'pdf'
                  WHEN real_type IN ('xml','html','htm','json','yaml') THEN 'markup'
                  WHEN real_type IN ('nrt','nxdx') THEN 'niche_markup'
                  WHEN real_type = 'txt' THEN 'text'
                  ELSE NULL
                END AS category,
                IIF(fixed_type LIKE '%;lz%', 1, 0) AS gzip
            FROM {tbl_blob_data} BD
              JOIN {tbl_gocc_tr_event} TR ON BD.HostId = TR.Id
              JOIN {tbl_gocc} OC ON TR.WId = OC.Id
              LEFT JOIN {tbl_std_occ_type} SOT ON OC.OccurrenceStdOccType_rId = SOT.Id
            WHERE OC.AccessControlList IS NULL
              AND TR.AccessControlList IS NULL
              AND BD.Id > ?
              AND LEFT(BD.HostId,8) IN ('{host_id_filter}')
            ORDER BY BD.Id
        )
        SELECT * FROM cte;
        """

        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                current_id = last_processed_id

                while True:
                    logger.debug(f"Fetching batch starting from ID: {current_id}")
                    cursor.execute(query, (batch_size, current_id))

                    # Fetch all rows for this batch
                    rows = cursor.fetchall()
                    if not rows:
                        logger.info("No more records to process")
                        break

                    # Convert to list of dictionaries
                    columns = [desc[0] for desc in cursor.description]
                    batch = [dict(zip(columns, row)) for row in rows]

                    # Update current_id for next iteration
                    current_id = max(record['Id'] for record in batch)

                    logger.info(f"Fetched batch of {len(batch)} records (IDs: {batch[0]['Id']} - {current_id})")
                    yield batch

        except Exception as e:
            logger.error(f"Error fetching batch: {e}")
            raise

    def insert_batch(self, table_name: str, records: List[Dict[str, Any]]) -> bool:
        """Insert a batch of processed records into destination table.

        Args:
            table_name: Destination table name
            records: List of processed record dictionaries

        Returns:
            True if successful, False otherwise
        """
        if not records:
            return True

        # Build 4-part table name
        full_table_name = self._build_full_table_name(table_name)

        # Build insert query dynamically based on record keys
        columns = list(records[0].keys())
        placeholders = ', '.join(['?' for _ in columns])
        column_names = ', '.join(columns)

        query = f"""
        INSERT INTO {full_table_name} ({column_names})
        VALUES ({placeholders})
        """

        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # Prepare data for executemany
                data = []
                for record in records:
                    row = tuple(record[col] for col in columns)
                    data.append(row)

                cursor.executemany(query, data)
                conn.commit()

                logger.info(f"Successfully inserted {len(records)} records into {table_name}")
                return True

        except Exception as e:
            logger.error(f"Error inserting batch into {table_name}: {e}")
            return False

    def execute_script(self, sql_script: str) -> None:
        """Execute a multi-statement SQL script."""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                # Split script into individual statements and execute
                # This is a basic split; for more complex scripts, a proper SQL parser might be needed
                statements = [s.strip() for s in sql_script.split('GO') if s.strip()]
                for statement in statements:
                    if statement:
                        cursor.execute(statement)
                        conn.commit() # Commit after each statement for DDL
                logger.info("SQL script executed successfully.")
        except Exception as e:
            logger.error(f"Failed to execute SQL script: {e}")
            raise
