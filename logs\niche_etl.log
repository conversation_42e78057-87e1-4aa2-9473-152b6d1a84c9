2025-06-17 09:02:47 | INFO | src.logging_setup:setup_logging:46 | Logging initialized - Level: INFO, File: logs\niche_etl.log
2025-06-17 09:02:47 | INFO | __main__:cli:39 | Niche Text ETL starting with config: config.yaml
2025-06-17 09:02:47 | INFO | src.pipeline:__init__:108 | Pipeline initialized successfully
2025-06-17 09:02:47 | INFO | src.pipeline:_test_connections:175 | Testing database connections...
2025-06-17 09:03:02 | ERROR | src.database:get_connection:75 | Database connection error: ('08001', '[08001] [Microsoft][ODBC Driver 17 for SQL Server]Named Pipes Provider: Could not open a connection to SQL Server [53].  (53) (SQLDriverConnect); [08001] [Microsoft][ODBC Driver 17 for SQL Server]Login timeout expired (0); [08001] [Microsoft][ODBC Driver 17 for SQL Server]Invalid connection string attribute (0); [08001] [Microsoft][ODBC Driver 17 for SQL Server]A network-related or instance-specific error has occurred while establishing a connection to SQL Server. Server is not found or not accessible. Check if instance name is correct and if SQL Server is configured to allow remote connections. For more information see SQL Server Books Online. (53)')
2025-06-17 09:03:02 | ERROR | src.database:test_connection:93 | Connection test failed: ('08001', '[08001] [Microsoft][ODBC Driver 17 for SQL Server]Named Pipes Provider: Could not open a connection to SQL Server [53].  (53) (SQLDriverConnect); [08001] [Microsoft][ODBC Driver 17 for SQL Server]Login timeout expired (0); [08001] [Microsoft][ODBC Driver 17 for SQL Server]Invalid connection string attribute (0); [08001] [Microsoft][ODBC Driver 17 for SQL Server]A network-related or instance-specific error has occurred while establishing a connection to SQL Server. Server is not found or not accessible. Check if instance name is correct and if SQL Server is configured to allow remote connections. For more information see SQL Server Books Online. (53)')
2025-06-17 09:09:14 | INFO | src.logging_setup:setup_logging:46 | Logging initialized - Level: INFO, File: logs\niche_etl.log
2025-06-17 09:09:14 | INFO | __main__:cli:37 | Niche Text ETL starting with config: config.yaml
2025-06-17 09:10:01 | INFO | src.logging_setup:setup_logging:46 | Logging initialized - Level: INFO, File: logs\niche_etl.log
2025-06-17 09:10:01 | INFO | __main__:cli:37 | Niche Text ETL starting with config: config.yaml
2025-06-17 09:10:34 | INFO | src.logging_setup:setup_logging:46 | Logging initialized - Level: INFO, File: logs\niche_etl.log
2025-06-17 09:10:34 | INFO | __main__:cli:37 | Niche Text ETL starting with config: config.yaml
2025-06-17 09:11:00 | INFO | src.logging_setup:setup_logging:46 | Logging initialized - Level: INFO, File: logs\niche_etl.log
2025-06-17 09:11:00 | INFO | __main__:cli:37 | Niche Text ETL starting with config: config.yaml
2025-06-18 13:55:47 | INFO | src.logging_setup:setup_logging:46 | Logging initialized - Level: INFO, File: logs\niche_etl.log
2025-06-18 13:55:47 | INFO | __main__:main:160 | Niche Text ETL starting with simplified execution model
2025-06-18 13:55:47 | INFO | __main__:main:163 | Initializing pipeline...
2025-06-18 13:55:47 | INFO | src.config_validator:validate_all:55 | Starting comprehensive configuration validation...
2025-06-18 13:55:47 | WARNING | src.config_validator:add_warning:22 | Configuration warning: Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed
2025-06-18 13:55:47 | INFO | src.config_validator:set_module_flag:34 | Module 'email_enabled' is disabled
2025-06-18 13:55:59 | INFO | src.config_validator:_validate_embedding_module:169 | Text embedding model accessibility test successful
2025-06-18 13:55:59 | INFO | src.config_validator:set_module_flag:34 | Module 'embedding_enabled' is enabled
2025-06-18 13:55:59 | INFO | src.config_validator:_log_validation_summary:240 | Configuration validation completed
2025-06-18 13:55:59 | INFO | src.config_validator:_log_validation_summary:241 | Overall validation status: PASSED
2025-06-18 13:55:59 | INFO | src.config_validator:_log_validation_summary:242 | Warnings: 1
2025-06-18 13:55:59 | INFO | src.config_validator:_log_validation_summary:243 | Errors: 0
2025-06-18 13:55:59 | INFO | src.config_validator:_log_validation_summary:246 | Module status:
2025-06-18 13:55:59 | INFO | src.config_validator:_log_validation_summary:249 |   - email_enabled: DISABLED
2025-06-18 13:55:59 | INFO | src.config_validator:_log_validation_summary:249 |   - embedding_enabled: ENABLED
2025-06-18 13:55:59 | WARNING | src.config_validator:_log_validation_summary:252 | Configuration warnings found:
2025-06-18 13:55:59 | WARNING | src.config_validator:_log_validation_summary:254 |   - Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed
2025-06-18 13:55:59 | INFO | src.embedding.factory:create_provider:46 | Created embedding provider: sentence_transformers
2025-06-18 13:55:59 | INFO | src.embedding.processor:_initialize_provider:45 | Initialized embedding provider: sentence_transformers
2025-06-18 13:55:59 | INFO | src.pipeline:__init__:120 | Text embedding processor initialized
2025-06-18 13:55:59 | INFO | src.pipeline:__init__:125 | Pipeline initialized successfully
2025-06-18 13:55:59 | INFO | __main__:main:169 | Validating configuration...
2025-06-18 13:55:59 | INFO | src.pipeline:_test_connections:220 | Testing database connections...
2025-06-18 13:55:59 | ERROR | src.database:get_connection:87 | Database connection error: ('28000', "[28000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Login failed for user 'HRPS\\HRPS30783'. (18456) (SQLDriverConnect); [28000] [Microsoft][ODBC Driver 17 for SQL Server]Invalid connection string attribute (0); [28000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Login failed for user 'HRPS\\HRPS30783'. (18456); [28000] [Microsoft][ODBC Driver 17 for SQL Server]Invalid connection string attribute (0)")
2025-06-18 13:55:59 | ERROR | src.database:test_connection:105 | Connection test failed: ('28000', "[28000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Login failed for user 'HRPS\\HRPS30783'. (18456) (SQLDriverConnect); [28000] [Microsoft][ODBC Driver 17 for SQL Server]Invalid connection string attribute (0); [28000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Login failed for user 'HRPS\\HRPS30783'. (18456); [28000] [Microsoft][ODBC Driver 17 for SQL Server]Invalid connection string attribute (0)")
2025-06-18 13:55:59 | ERROR | __main__:main:176 | Configuration validation failed:
2025-06-18 13:55:59 | ERROR | __main__:main:178 |   - Database connection error: Source database connection failed
2025-06-18 13:55:59 | ERROR | __main__:main:225 | Configuration Error: Configuration validation failed
2025-06-18 13:57:09 | INFO | src.logging_setup:setup_logging:46 | Logging initialized - Level: INFO, File: logs\niche_etl.log
2025-06-18 13:57:09 | INFO | __main__:main:160 | Niche Text ETL starting with simplified execution model
2025-06-18 13:57:09 | INFO | __main__:main:163 | Initializing pipeline...
2025-06-18 13:57:09 | INFO | src.config_validator:validate_all:55 | Starting comprehensive configuration validation...
2025-06-18 13:57:09 | WARNING | src.config_validator:add_warning:22 | Configuration warning: Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed
2025-06-18 13:57:09 | INFO | src.config_validator:set_module_flag:34 | Module 'email_enabled' is disabled
2025-06-18 13:57:20 | INFO | src.config_validator:_validate_embedding_module:169 | Text embedding model accessibility test successful
2025-06-18 13:57:20 | INFO | src.config_validator:set_module_flag:34 | Module 'embedding_enabled' is enabled
2025-06-18 13:57:20 | INFO | src.config_validator:_log_validation_summary:240 | Configuration validation completed
2025-06-18 13:57:20 | INFO | src.config_validator:_log_validation_summary:241 | Overall validation status: PASSED
2025-06-18 13:57:20 | INFO | src.config_validator:_log_validation_summary:242 | Warnings: 1
2025-06-18 13:57:20 | INFO | src.config_validator:_log_validation_summary:243 | Errors: 0
2025-06-18 13:57:20 | INFO | src.config_validator:_log_validation_summary:246 | Module status:
2025-06-18 13:57:20 | INFO | src.config_validator:_log_validation_summary:249 |   - email_enabled: DISABLED
2025-06-18 13:57:20 | INFO | src.config_validator:_log_validation_summary:249 |   - embedding_enabled: ENABLED
2025-06-18 13:57:20 | WARNING | src.config_validator:_log_validation_summary:252 | Configuration warnings found:
2025-06-18 13:57:20 | WARNING | src.config_validator:_log_validation_summary:254 |   - Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed
2025-06-18 13:57:20 | INFO | src.embedding.factory:create_provider:46 | Created embedding provider: sentence_transformers
2025-06-18 13:57:20 | INFO | src.embedding.processor:_initialize_provider:45 | Initialized embedding provider: sentence_transformers
2025-06-18 13:57:20 | INFO | src.pipeline:__init__:120 | Text embedding processor initialized
2025-06-18 13:57:20 | INFO | src.pipeline:__init__:125 | Pipeline initialized successfully
2025-06-18 13:57:20 | INFO | __main__:main:169 | Validating configuration...
2025-06-18 13:57:20 | INFO | src.pipeline:_test_connections:220 | Testing database connections...
2025-06-18 13:57:21 | INFO | src.pipeline:_test_connections:228 | Database connections successful
2025-06-18 13:57:21 | WARNING | __main__:main:186 | Configuration warnings:
2025-06-18 13:57:21 | WARNING | __main__:main:188 |   - Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed
2025-06-18 13:57:21 | INFO | __main__:main:190 | Configuration validation completed
2025-06-18 13:57:21 | INFO | __main__:main:199 | Starting ETL pipeline execution...
2025-06-18 13:57:21 | INFO | src.pipeline:run:133 | Starting Niche Text ETL Pipeline
2025-06-18 13:57:21 | INFO | src.pipeline:_test_connections:220 | Testing database connections...
2025-06-18 13:57:21 | INFO | src.pipeline:_test_connections:228 | Database connections successful
2025-06-18 13:57:21 | ERROR | src.database:get_connection:87 | Database connection error: ('42S02', "[42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid object name 'NicheRMSReport.dbo.NicheBlobETLCheckpoints'. (208) (SQLExecDirectW); [42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Statement(s) could not be prepared. (8180)")
2025-06-18 13:57:21 | WARNING | src.database:get_last_processed_id:126 | Could not retrieve last processed ID for niche_text_etl: ('42S02', "[42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid object name 'NicheRMSReport.dbo.NicheBlobETLCheckpoints'. (208) (SQLExecDirectW); [42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Statement(s) could not be prepared. (8180)"). Starting from 0.
2025-06-18 13:57:21 | ERROR | src.database:get_connection:87 | Database connection error: ('42S22', "[42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'fixed_type'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'fixed_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'fixed_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Statement(s) could not be prepared. (8180)")
2025-06-18 13:57:21 | ERROR | src.database:fetch_batch:248 | Error fetching batch: ('42S22', "[42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'fixed_type'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'fixed_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'fixed_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Statement(s) could not be prepared. (8180)")
2025-06-18 13:57:21 | ERROR | src.pipeline:run:215 | Pipeline failed: ('42S22', "[42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'fixed_type'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'fixed_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'fixed_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Statement(s) could not be prepared. (8180)")
2025-06-18 13:57:21 | ERROR | __main__:main:238 | Unexpected error: ('42S22', "[42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'fixed_type'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'fixed_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'fixed_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Statement(s) could not be prepared. (8180)")
